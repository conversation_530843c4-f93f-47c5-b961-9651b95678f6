"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { Mail, Phone, MapPin, ArrowRight } from "lucide-react";

export default function Contact() {
  return (
    <section id="contact" className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Get in Touch</h2>
          <p className="text-xl text-muted-foreground">Have questions? We'd love to hear from you</p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <div className="space-y-8">
            <div className="space-y-6">
              {[{Icon:Mail,title:"Email Us",sub:"<EMAIL>"},{Icon:Phone,title:"Call Us",sub:"+****************"},{Icon:MapPin,title:"Visit Us",sub:(<><span>123 Business Ave, Suite 100</span><br/>New York, NY 10001</>)}].map(({Icon,title,sub})=> (
                <div key={title} className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center">
                    <Icon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{title}</h3>
                    <p className="text-muted-foreground">{sub}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="relative">
              <div className="aspect-video rounded-3xl overflow-hidden shadow-lg">
                <ImageWithFallback
                  src="https://images.unsplash.com/photo-1664575600397-88e370cb46b8?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxzbWFsbCUyMGJ1c2luZXNzJTIwb3duZXJ8ZW58MXx8fHwxNzU2ODk4NzkwfDA&ixlib=rb-4.1.0&q=80&w=1080"
                  alt="Small business owner"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
          <Card className="p-8 rounded-3xl border-0 shadow-lg">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold">Send us a message</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">First Name</label>
                    <Input placeholder="John" className="rounded-xl" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Last Name</label>
                    <Input placeholder="Doe" className="rounded-xl" />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <Input type="email" placeholder="<EMAIL>" className="rounded-xl" />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Subject</label>
                  <Input placeholder="How can we help?" className="rounded-xl" />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Message</label>
                  <Textarea placeholder="Tell us more about your inquiry..." className="rounded-xl min-h-32" />
                </div>
              </div>
              <Button className="w-full h-12 rounded-2xl bg-primary hover:bg-primary/90">
                Send Message
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
}

