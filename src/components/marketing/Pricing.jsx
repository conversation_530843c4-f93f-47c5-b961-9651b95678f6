import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";

export default function Pricing({ pricingPlans, onGetStarted }) {
  return (
    <section id="pricing" className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Simple, Transparent Pricing</h2>
          <p className="text-xl text-muted-foreground">Choose the plan that's right for your business</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <Card key={index} className={`p-8 rounded-3xl relative ${plan.popular ? "border-2 border-primary shadow-lg scale-105" : "border-0 shadow-sm"}`}>
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">Most Popular</Badge>
              )}
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold">{plan.name}</h3>
                  <p className="text-muted-foreground">{plan.description}</p>
                </div>
                <div>
                  <div className="text-4xl font-bold">
                    {plan.price}
                    {plan.period !== "contact us" && (
                      <span className="text-lg text-muted-foreground font-normal">/{plan.period}</span>
                    )}
                  </div>
                </div>
                <div className="space-y-3">
                  {plan.features.map((f, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary" />
                      <span>{f}</span>
                    </div>
                  ))}
                </div>
                <Button onClick={onGetStarted} className={`w-full h-12 rounded-2xl ${plan.popular ? "bg-primary hover:bg-primary/90" : "bg-secondary text-foreground hover:bg-secondary/80"}`}>
                  {plan.price === "Custom" ? "Contact Sales" : "Get Started"}
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

