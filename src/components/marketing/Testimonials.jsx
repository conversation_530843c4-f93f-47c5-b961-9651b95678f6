import { Card } from "@/components/ui/card";
import { Star } from "lucide-react";

export default function Testimonials({ testimonials }) {
  return (
    <section className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">What Our Users Say</h2>
          <p className="text-xl text-muted-foreground">Join thousands of satisfied businesses using VendorHub</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((t, i) => (
            <Card key={i} className="p-8 rounded-3xl border-0 shadow-sm">
              <div className="space-y-6">
                <div className="flex items-center gap-1">
                  {Array.from({ length: t.rating }, (_, j) => (
                    <Star key={j} className="w-5 h-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-lg leading-relaxed">"{t.content}"</blockquote>
                <div>
                  <div className="font-semibold">{t.name}</div>
                  <div className="text-sm text-muted-foreground">{t.role}</div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

