"use client";

import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getAvailableRoles } from "@/constants/navLinks";
import { 
  getCurrentUserRole, 
  setCurrentUserRole, 
  getRoleDisplayName 
} from "@/utils/roleUtils";

export function RoleSwitcher({ onRoleChange }) {
  const [currentRole, setCurrentRole] = useState("");
  const availableRoles = getAvailableRoles();

  useEffect(() => {
    setCurrentRole(getCurrentUserRole());
  }, []);

  const handleRoleChange = (newRole) => {
    setCurrentRole(newRole);
    setCurrentUserRole(newRole);
    
    // Notify parent component about role change
    if (onRoleChange) {
      onRoleChange(newRole);
    }
    
    // Refresh the page to update the sidebar
    window.location.reload();
  };

  return (
    <div className="flex items-center gap-3 p-4 border rounded-lg bg-card">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Current Role:</span>
        <Badge variant="secondary">
          {getRoleDisplayName(currentRole)}
        </Badge>
      </div>
      
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Switch to:</span>
        <Select value={currentRole} onValueChange={handleRoleChange}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select a role" />
          </SelectTrigger>
          <SelectContent>
            {availableRoles.map((role) => (
              <SelectItem key={role} value={role}>
                {getRoleDisplayName(role)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
