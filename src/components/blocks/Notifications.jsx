import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  X,
  CheckCircle,
  AlertCircle,
  Info,
  DollarSign,
  UserPlus,
  MessageSquare,
  Star,
  Bell,
  BellOff,
  Trash2,
} from "lucide-react";

export function Notifications() {
  const [filter, setFilter] = useState("all");
  const [open, setOpen] = useState(false);
  const [notifications, setNotifications] = useState([
    {
      id: "1",
      type: "info",
      title: "New Enquiry Received",
      message: "<PERSON> sent an enquiry for catering services",
      time: "5 minutes ago",
      isRead: false,
      actionable: true,
      category: "enquiry",
    },
    {
      id: "2",
      type: "success",
      title: "Payment Confirmed",
      message: "Registration fee of $99 has been processed successfully",
      time: "1 hour ago",
      isRead: false,
      category: "payment",
    },
    {
      id: "3",
      type: "warning",
      title: "Profile Incomplete",
      message: "Please complete your business profile to get more visibility",
      time: "2 hours ago",
      isRead: true,
      actionable: true,
      category: "vendor",
    },
    {
      id: "4",
      type: "success",
      title: "New Review",
      message: "You received a 5-star review from Alice Johnson",
      time: "4 hours ago",
      isRead: true,
      category: "review",
    },
    {
      id: "5",
      type: "info",
      title: "System Maintenance",
      message: "Scheduled maintenance tonight from 2 AM - 4 AM EST",
      time: "1 day ago",
      isRead: true,
      category: "system",
    },
    {
      id: "6",
      type: "success",
      title: "New Vendor Approved",
      message: "TechFix Solutions has been successfully onboarded",
      time: "2 days ago",
      isRead: true,
      category: "vendor",
    },
  ]);

  const getIcon = (type, category) => {
    if (category === "enquiry") return MessageSquare;
    if (category === "payment") return DollarSign;
    if (category === "review") return Star;
    if (category === "vendor" || category === "user") return UserPlus;

    switch (type) {
      case "success":
        return CheckCircle;
      case "warning":
      case "error":
        return AlertCircle;
      default:
        return Info;
    }
  };

  const getIconColor = (type) => {
    switch (type) {
      case "success":
        return "text-green-500";
      case "warning":
        return "text-yellow-500";
      case "error":
        return "text-red-500";
      default:
        return "text-blue-500";
    }
  };

  const getBadgeColor = (category) => {
    switch (category) {
      case "enquiry":
        return "bg-blue-100 text-blue-800";
      case "payment":
        return "bg-green-100 text-green-800";
      case "review":
        return "bg-yellow-100 text-yellow-800";
      case "vendor":
        return "bg-purple-100 text-purple-800";
      case "system":
        return "bg-muted text-foreground";
      default:
        return "bg-muted text-foreground";
    }
  };

  const markAsRead = (id) => {
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notif) => ({ ...notif, isRead: true }))
    );
  };

  const deleteNotification = (id) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id));
  };

  const filteredNotifications = notifications.filter((notif) => {
    if (filter === "unread") return !notif.isRead;
    if (filter === "read") return notif.isRead;
    return true;
  });

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Bell className="w-4 h-4 mr-2" />
          Notifications
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md p-0 rounded-3xl">
        <Card className="bg-background rounded-3xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-border">
            <DialogHeader className="w-full flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-2xl flex items-center justify-center">
                  <Bell className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <DialogTitle>
                    <h2 className="text-xl font-bold text-foreground">
                      Notifications
                    </h2>
                  </DialogTitle>
                  {unreadCount > 0 && (
                    <p className="text-sm text-muted-foreground">
                      {unreadCount} unread notification
                      {unreadCount !== 1 ? "s" : ""}
                    </p>
                  )}
                </div>
              </div>
            </DialogHeader>

            {/* Filter Tabs */}
            <div className="flex gap-2 mt-4">
              {["all", "unread", "read"].map((filterOption) => (
                <Button
                  key={filterOption}
                  variant={filter === filterOption ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setFilter(filterOption)}
                  className="rounded-xl capitalize"
                >
                  {filterOption}
                  {filterOption === "unread" && unreadCount > 0 && (
                    <Badge className="ml-2 bg-primary text-white text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>

          {/* Actions */}
          {unreadCount > 0 && (
            <div className="px-6 py-3 border-b border-border">
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-primary hover:text-primary/80 rounded-xl"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Mark all as read
              </Button>
            </div>
          )}

          {/* Notifications List */}
          <ScrollArea className="flex-1 max-h-96">
            {filteredNotifications.length === 0 ? (
              <div className="p-12 text-center text-muted-foreground">
                <BellOff className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <h3 className="font-medium mb-2">No notifications</h3>
                <p className="text-sm">
                  {filter === "unread"
                    ? "You're all caught up!"
                    : "You don't have any notifications yet."}
                </p>
              </div>
            ) : (
              <div className="p-2">
                {filteredNotifications.map((notification) => {
                  const Icon = getIcon(
                    notification.type,
                    notification.category
                  );

                  return (
                    <div
                      key={notification.id}
                      className={`p-4 rounded-2xl mb-2 transition-colors cursor-pointer group hover:bg-muted/50 ${
                        !notification.isRead ? "bg-primary/5" : ""
                      }`}
                      onClick={() =>
                        !notification.isRead && markAsRead(notification.id)
                      }
                    >
                      <div className="flex items-start gap-3">
                        <div
                          className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                            notification.isRead ? "bg-muted" : "bg-primary/10"
                          }`}
                        >
                          <Icon
                            className={`w-4 h-4 ${
                              notification.isRead
                                ? "text-muted-foreground"
                                : getIconColor(notification.type)
                            }`}
                          />
                        </div>

                        <div className="flex-1 space-y-1">
                          <div className="flex items-start justify-between">
                            <h4
                              className={`font-medium text-sm ${
                                !notification.isRead
                                  ? "text-foreground"
                                  : "text-muted-foreground"
                              }`}
                            >
                              {notification.title}
                            </h4>
                            <div className="flex items-center gap-2">
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-primary rounded-full" />
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteNotification(notification.id);
                                }}
                                className="opacity-0 group-hover:opacity-100 p-1 h-auto rounded-lg"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>

                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {notification.message}
                          </p>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-muted-foreground">
                              {notification.time}
                            </span>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${getBadgeColor(
                                notification.category
                              )}`}
                            >
                              {notification.category}
                            </Badge>
                          </div>

                          {notification.actionable && (
                            <div className="pt-2">
                              <Button
                                size="sm"
                                className="h-8 rounded-lg text-xs"
                              >
                                View Details
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
