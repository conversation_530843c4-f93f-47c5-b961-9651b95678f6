"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";

export default function LocationSelector() {
  const recentLocations = ["Antarli, Thane"];
  const [selectedLocation, setSelectedLocation] = useState(recentLocations[0]);
  const trendingAreas = [
    "Mira Road East, Thane",
    "Kandivali, Mumbai",
    "Malad West, Mumbai",
    "Kandivali West, Mumbai",
    "Thane West, Thane",
    "Nalasopara East, Palghar",
  ];

  const handleSelect = (value) => {
    setSelectedLocation(value);
  };

  return (
    <div className="w-[300px]">
      <Select value={selectedLocation} onValueChange={handleSelect}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select Location" />
        </SelectTrigger>

        <SelectContent>
          {/* Detect Location */}
          <SelectGroup>
            <SelectItem value="detect">📍 Detect Location</SelectItem>
          </SelectGroup>

          {/* Recent Locations */}
          <SelectGroup>
            <SelectLabel className="text-muted-foreground text-xs">
              Recent Locations
            </SelectLabel>
            {recentLocations.map((location, index) => (
              <SelectItem key={index} value={location}>
                {location}
              </SelectItem>
            ))}
          </SelectGroup>

          {/* Trending Areas */}
          <SelectGroup>
            <SelectLabel className="text-muted-foreground text-xs">
              Trending Areas
            </SelectLabel>
            {trendingAreas.map((area, index) => (
              <SelectItem key={index} value={area}>
                {area}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
