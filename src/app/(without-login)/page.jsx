"use client";

import LocationSelector from "@/components/blocks/LocationSelector";
import React from "react";
import Banners from "./components/Banners";
import { Input } from "@/components/ui/input";
import { categories } from "@/constants";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import Link from "next/link";

export default function Home() {
  return (
    <div>
      {/* Search Bar */}
      <div className="w-full flex items-center justify-center p-6">
        <div className="w-full min-w-4xl max-w-6xl flex items-center justify-start gap-4">
          <LocationSelector />
          <Input
            placeholder="Search for vendors, services..."
            className="w-full rounded-xl"
          />
        </div>
      </div>

      {/* Banners */}
      <div className="w-full flex items-center justify-center p-6">
        <Banners />
      </div>

      {/* Categories */}
      <div className="w-full flex items-center justify-center p-6">
        <div className="w-full min-w-4xl max-w-6xl grid grid-cols-6 gap-4">
          {categories.map((category) => (
            <Link
              href={"/categories/"}
              key={category.id}
              className="p-1 rounded-2xl border-2 shadow w-[150px] aspect-square flex flex-col items-center justify-center"
            >
              <ImageWithFallback
                src={category.icon}
                className="w-12 rounded-md"
              />
              <h3 className="font-semibold text-center">{category.name}</h3>
              <p className="text-sm text-muted-foreground text-center">
                {category.count} vendors
              </p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
