import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON> } from "@/components/ui/badge";
import { ArrowR<PERSON>, BarChart3, Users } from "lucide-react";
import { ImageWithFallback } from "@/components/ui/image-fallback";

export default function HeroSection({ onGetStarted }) {
  return (
    <section className="px-6 py-16">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge className="bg-primary/10 text-primary border-primary/20 rounded-full px-4 py-1">
                Join 10,000+ Successful Vendors
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold leading-tight">
                Grow Your Business with{" "}
                <span className="text-primary">VendorHub</span>
              </h1>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Connect with thousands of customers, manage your business
                efficiently, and grow your revenue with our comprehensive vendor
                platform.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={onGetStarted}
                size="lg"
                className="rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground h-12 px-8"
              >
                Start Your Journey
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="rounded-xl h-12 px-8"
              >
                Watch Demo
              </Button>
            </div>

            <div className="flex items-center gap-8 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">10K+</div>
                <div className="text-sm text-muted-foreground">
                  Active Vendors
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">50K+</div>
                <div className="text-sm text-muted-foreground">
                  Monthly Customers
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">4.9★</div>
                <div className="text-sm text-muted-foreground">
                  Average Rating
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1608222351212-18fe0ec7b13b?auto=format&fit=crop&w=1080&q=80"
                alt="Business dashboard"
                className="w-full h-[400px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-foreground/10 to-transparent" />
            </div>

            <div className="absolute -bottom-4 -left-4 bg-card rounded-xl p-4 shadow-lg border">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold text-foreground">+150%</div>
                  <div className="text-xs text-muted-foreground">
                    Revenue Growth
                  </div>
                </div>
              </div>
            </div>

            <div className="absolute -top-4 -right-4 bg-card rounded-xl p-4 shadow-lg border">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold text-foreground">2.5K</div>
                  <div className="text-xs text-muted-foreground">
                    New Customers
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
