import { Card } from "@/components/ui/card";
import { Star } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    business: "Johnson's Catering",
    rating: 5,
    comment:
      "VendorHub helped me reach more customers and grow my catering business by 150% in just 6 months!",
  },
  {
    name: "<PERSON>",
    business: "TechFix Solutions",
    rating: 5,
    comment:
      "The platform is incredibly easy to use. I get regular inquiries and the analytics help me understand my customers better.",
  },
  {
    name: "<PERSON>",
    business: "Creative Designs Studio",
    rating: 5,
    comment:
      "Professional setup, great customer support, and excellent results. Highly recommended for any service business.",
  },
];

export default function TestimonialsSection() {
  return (
    <section className="px-6 py-16 bg-card/50 backdrop-blur-sm">
      <div className="max-w-6xl mx-auto">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-3xl font-bold">What Our Vendors Say</h2>
          <p className="text-lg text-muted-foreground">
            Hear from successful vendors who have grown their business with VendorHub.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="p-6 rounded-2xl border shadow-sm">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-primary text-primary" />
                  ))}
                </div>
                <p className="text-muted-foreground italic">"{testimonial.comment}"</p>
                <div>
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.business}</div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

