import { Button } from "@/components/ui/button";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { ArrowR<PERSON>, Zap, Shield, Headphones } from "lucide-react";

const benefits = [
  {
    icon: Zap,
    title: "Quick Setup",
    description:
      "Get your business online in minutes with our streamlined onboarding process",
  },
  {
    icon: Shield,
    title: "Verified Listings",
    description: "All vendor profiles are verified for trust and credibility",
  },
  {
    icon: Headphones,
    title: "24/7 Support",
    description:
      "Get help whenever you need it with our dedicated support team",
  },
];

export default function BenefitsSection({ onGetStarted }) {
  return (
    <section className="px-6 py-16">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="relative">
            <ImageWithFallback
              src="https://images.unsplash.com/photo-1642522029691-029b5a432954?auto=format&fit=crop&w=1080&q=80"
              alt="Business meeting"
              className="w-full h-[400px] object-cover rounded-2xl"
            />
          </div>

          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold">Why Choose VendorHub?</h2>
              <p className="text-lg text-muted-foreground">
                Join thousands of successful vendors who trust VendorHub to grow
                their business.
              </p>
            </div>

            <div className="space-y-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <benefit.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">{benefit.title}</h3>
                    <p className="text-muted-foreground">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <Button
              onClick={onGetStarted}
              size="lg"
              className="rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground h-12 px-8"
            >
              Join VendorHub Today
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
