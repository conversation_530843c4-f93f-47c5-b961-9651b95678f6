"use client";

import { useRouter } from "next/navigation";
import HeroS<PERSON><PERSON> from "./_components/HeroSection";
import FeaturesSection from "./_components/FeaturesSection";
import BenefitsSection from "./_components/BenefitsSection";
import TestimonialsSection from "./_components/TestimonialsSection";
import CTASection from "./_components/CTASection";
import Footer from "@/components/marketing/Footer";

export default function VendorOnboardingLanding() {
  const router = useRouter();

  const onGetStarted = () => {
    router.push("/register");
  };

  const onLogin = () => {
    router.push("/login");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5">
      <div className="mt-6" />
      <HeroSection onGetStarted={onGetStarted} />

      <FeaturesSection />

      <BenefitsSection onGetStarted={onGetStarted} />

      <TestimonialsSection />

      <CTASection onGetStarted={onGetStarted} onLogin={onLogin} />

      <Footer />
    </div>
  );
}
