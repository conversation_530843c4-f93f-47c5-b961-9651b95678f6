"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import {
  Star,
  TrendingUp,
  MapPin,
  Phone,
  MessageCircle,
  Filter,
  CheckCircle,
  Eye,
} from "lucide-react";
import { displayVendors } from "@/constants";
import { useRouter } from "next/navigation";
import { VendorDetailModal } from "@/components/blocks/VendorDetailModel";

export default function VendorsListingPage() {
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [filters, setFilters] = useState({
    topRated: false,
    verified: false,
  });
  const filteredVendors = displayVendors.filter((v) => {
    if (filters.topRated && v.rating < 4.5) return false; // Filter vendors with rating < 4
    if (filters.verified && !v.verified) return false; // Filter vendors not verified
    return true;
  });

  const router = useRouter();

  const handlePushLogin = () => {
    router.push("/login");
  };

  return (
    <main className="min-h-screen bg-background pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Heading */}
        <div className="mb-4 text-sm text-muted-foreground">
          Thane • Hardware Shops in Thane • Listings
        </div>
        <h1 className="text-2xl md:text-3xl font-semibold mb-6">
          Popular Hardware Shops in Thane
        </h1>

        {/* Toolbar */}
        <div className="flex flex-wrap items-center gap-3 mb-6">
          <Button
            variant={filters.topRated ? "default" : "outline"}
            className="rounded-xl"
            onClick={() =>
              setFilters((prev) => ({ ...prev, topRated: !prev.topRated }))
            }
          >
            <Star className="w-4 h-4 mr-2" /> Top Rated
          </Button>

          <Button
            variant={filters.verified ? "default" : "outline"}
            className="rounded-xl"
            onClick={() =>
              setFilters((prev) => ({ ...prev, verified: !prev.verified }))
            }
          >
            <CheckCircle className="w-4 h-4 mr-2" /> Verified
          </Button>

          <Button variant="outline" className="rounded-xl">
            <Filter className="w-4 h-4 mr-2" /> All Filters
          </Button>
        </div>

        {/* Vendor Cards */}
        <div className="space-y-6">
          {filteredVendors.map((v) => (
            <Card key={v.id} className="p-4 md:p-5 rounded-2xl shadow-sm">
              <div className="grid grid-cols-1 sm:grid-cols-[160px_1fr] gap-4">
                {/* Image */}
                <div className="rounded-xl overflow-hidden border bg-card">
                  <div className="aspect-[4/4]">
                    <ImageWithFallback
                      src={v.image}
                      alt={v.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="flex flex-col justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center flex-wrap gap-3">
                      <h3 className="text-lg md:text-xl font-semibold text-foreground">
                        {v.name}
                      </h3>
                      <Badge className="bg-primary/15 text-primary border-primary/20 rounded-lg">
                        <span className="flex items-center gap-1">
                          <Star className="w-4 h-4 fill-primary text-primary" />{" "}
                          {v.rating}
                        </span>
                      </Badge>
                      {v.trending && (
                        <Badge variant="secondary" className="rounded-lg">
                          <TrendingUp className="w-4 h-4 mr-1" /> Trending
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="w-4 h-4" />
                      <span>{v.address}</span>
                      <span className="mx-1">•</span>
                      <span>{v.distance}</span>
                    </div>

                    <div className="flex flex-wrap gap-2 pt-1">
                      {v.categories.map((c) => (
                        <Badge
                          key={c}
                          variant="secondary"
                          className="rounded-lg"
                        >
                          {c}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="mt-4 flex flex-wrap gap-3">
                    <Button
                      variant="outline"
                      className="rounded-xl"
                      onClick={handlePushLogin}
                    >
                      <Phone className="w-4 h-4 mr-2" /> Contact
                    </Button>
                    <Button
                      variant="outline"
                      className="rounded-xl"
                      onClick={handlePushLogin}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" /> WhatsApp
                    </Button>
                    <Button
                      onClick={() => setSelectedVendor(v)}
                      className="rounded-xl bg-primary hover:bg-primary/90"
                    >
                      <Eye className="w-4 h-4 ml-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {selectedVendor && (
        <VendorDetailModal
          vendor={selectedVendor}
          onClose={() => setSelectedVendor(null)}
        />
      )}
    </main>
  );
}
