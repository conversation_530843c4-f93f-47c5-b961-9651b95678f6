"use client";

import { ImageWithFallback } from "@/components/ui/image-fallback";
import { categories, subCategories } from "@/constants";
import Link from "next/link";
import React from "react";

export default function SubCategoriesPage() {
  return (
    <div>
      {/* Categories */}
      <div className="w-full flex items-center justify-center p-6">
        <div className="w-full min-w-4xl max-w-6xl grid grid-cols-6 gap-4">
          {subCategories.map((category) => (
            <Link
              href={"/vendors/listing"}
              key={category.id}
              className="p-1 rounded-2xl border-2 shadow w-[150px] aspect-square flex flex-col items-center justify-center"
            >
              <ImageWithFallback
                src={category.icon}
                className="w-12 rounded-md"
              />
              <h3 className="font-semibold text-center">{category.name}</h3>
              <p className="text-sm text-muted-foreground text-center">
                {category.count} vendors
              </p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
