import React from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, CheckCircle, Plus } from "lucide-react";
import { upcomingVisits, completedVisits } from "@/constants/sales";

export default function FieldVisitsPage() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Field Visits</h1>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            <Plus className="w-4 h-4 mr-2" />
            Schedule Visit
          </Button>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold mb-4">Upcoming Visits</h2>
            <div className="space-y-4">
              {upcomingVisits.map((visit) => (
                <Card key={visit.id} className="p-6 rounded-2xl">
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold">{visit.purpose}</h3>
                        <p className="text-muted-foreground">
                          {visit.location}
                        </p>
                        <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4" />
                          <span>
                            {visit.date} at {visit.time}
                          </span>
                        </div>
                      </div>
                      <Badge className="bg-blue-100 text-blue-800">
                        {visit.status}
                      </Badge>
                    </div>

                    <div className="flex gap-3">
                      <Button size="sm" className="rounded-lg">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Mark Complete
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-lg"
                      >
                        Reschedule
                      </Button>
                      <Button variant="ghost" size="sm" className="rounded-lg">
                        View Details
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold mb-4">Completed Visits</h2>
            <div className="space-y-4">
              {completedVisits.map((visit) => (
                <Card key={visit.id} className="p-6 rounded-2xl">
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold">{visit.purpose}</h3>
                        <p className="text-muted-foreground">
                          {visit.location}
                        </p>
                        <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4" />
                          <span>
                            {visit.date} at {visit.time}
                          </span>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {visit.status}
                      </Badge>
                    </div>

                    <div className="p-3 bg-muted/30 rounded-xl">
                      <span className="text-sm text-muted-foreground">
                        Outcome:
                      </span>
                      <p className="text-sm font-medium">{visit.outcome}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
