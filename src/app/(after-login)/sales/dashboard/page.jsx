"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  MapPin,
  Building2,
  DollarSign,
  Plus,
  Search,
  UserPlus,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
} from "lucide-react";
import { salesStats, upcomingVisits } from "@/constants/sales";
import { recentVendors } from "@/constants/vendors";

export default function SalesExecutiveDashboard() {
  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <main className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Sales Dashboard</h1>
              <p className="text-muted-foreground">
                Track your vendor onboarding performance and earnings
              </p>
            </div>
            <Button className="rounded-xl bg-primary hover:bg-primary/90">
              <UserPlus className="w-4 h-4 mr-2" />
              Onboard Vendor
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Building2 className="w-5 h-5 text-primary" />
                  <span className="text-sm text-green-600 font-medium">
                    {Math.round(
                      (salesStats.vendorsOnboarded /
                        salesStats.thisMonthTarget) *
                        100
                    )}
                    %
                  </span>
                </div>
                <div className="text-2xl font-bold">
                  {salesStats.vendorsOnboarded}
                </div>
                <div className="text-sm text-muted-foreground">
                  Vendors Onboarded
                  <div className="text-xs text-muted-foreground">
                    Target: {salesStats.thisMonthTarget}
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <DollarSign className="w-5 h-5 text-green-500" />
                  <span className="text-sm text-green-600 font-medium">
                    +18%
                  </span>
                </div>
                <div className="text-2xl font-bold">
                  ${salesStats.feesCollected}
                </div>
                <div className="text-sm text-muted-foreground">
                  Fees Collected
                </div>
              </div>
            </Card>

            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <TrendingUp className="w-5 h-5 text-green-500" />
                <div className="text-2xl font-bold">
                  ${salesStats.commissionEarned}
                </div>
                <div className="text-sm text-muted-foreground">
                  Commission Earned
                </div>
              </div>
            </Card>

            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <MapPin className="w-5 h-5 text-blue-500" />
                  <span className="text-sm text-muted-foreground">Today</span>
                </div>
                <div className="text-2xl font-bold">3</div>
                <div className="text-sm text-muted-foreground">
                  Scheduled Visits
                </div>
              </div>
            </Card>
          </div>

          {/* Progress Chart */}
          <Card className="p-6 rounded-2xl">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Monthly Progress</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Target Progress</span>
                  <span>
                    {salesStats.vendorsOnboarded}/{salesStats.thisMonthTarget}{" "}
                    vendors
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-3">
                  <div
                    className="bg-primary h-3 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(
                        (salesStats.vendorsOnboarded /
                          salesStats.thisMonthTarget) *
                          100,
                        100
                      )}%`,
                    }}
                  />
                </div>
              </div>
            </div>
          </Card>

          {/* Recent Activities */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Recent Onboardings</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentView("vendors")}
                  >
                    View All
                  </Button>
                </div>
                <div className="space-y-4">
                  {recentVendors.slice(0, 2).map((vendor) => (
                    <div key={vendor.id} className="p-4 bg-muted/30 rounded-xl">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold">
                              {vendor.businessName}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              {vendor.category} • {vendor.location}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Contact: {vendor.contactPerson}
                            </p>
                          </div>
                          <Badge
                            variant={
                              vendor.status === "approved"
                                ? "default"
                                : "secondary"
                            }
                            className={
                              vendor.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {vendor.status}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            Commission: ${vendor.commission}
                          </span>
                          <span className="text-muted-foreground">
                            {vendor.onboardedDate}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            <Card className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Upcoming Visits</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentView("visits")}
                  >
                    View All
                  </Button>
                </div>
                <div className="space-y-4">
                  {upcomingVisits.map((visit) => (
                    <div key={visit.id} className="p-4 bg-muted/30 rounded-xl">
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-sm">
                              {visit.purpose}
                            </h4>
                            <p className="text-xs text-muted-foreground">
                              {visit.location}
                            </p>
                          </div>
                          <Badge className="bg-blue-100 text-blue-800 text-xs">
                            {visit.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{visit.date}</span>
                          <span>•</span>
                          <span>{visit.time}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Contact: {visit.contact}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
