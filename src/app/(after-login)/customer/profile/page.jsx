import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

export default function Page() {
  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">My Profile</h1>
          <Button variant="outline" className="rounded-xl">
            Edit Profile
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Full Name</label>
                <Input value="John Doe" className="rounded-xl" readOnly />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input
                  value="<EMAIL>"
                  className="rounded-xl"
                  readOnly
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Phone</label>
                <Input value="+****************" className="rounded-xl" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Location</label>
                <Input value="New York, NY" className="rounded-xl" />
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold mb-4">Preferences</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Preferred Categories
                </label>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">Restaurants</Badge>
                  <Badge variant="outline">Technology</Badge>
                  <Badge variant="outline">Healthcare</Badge>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Notification Preferences
                </label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="email-notifications" />
                    <label htmlFor="email-notifications" className="text-sm">
                      Email notifications
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sms-notifications" />
                    <label htmlFor="sms-notifications" className="text-sm">
                      SMS notifications
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
