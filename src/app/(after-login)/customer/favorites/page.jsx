"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Filter, Heart, MapPin, Star } from "lucide-react";

const trendingVendors = [
  {
    id: 1,
    name: "<PERSON>'s Italian Kitchen",
    category: "Restaurants",
    rating: 4.8,
    reviews: 234,
    location: "Downtown, NY",
    image: "🍝",
    isVerified: true,
  },
  {
    id: 2,
    name: "TechFix Solutions",
    category: "Technology",
    rating: 4.9,
    reviews: 156,
    location: "Midtown, NY",
    image: "💻",
    isVerified: true,
  },
  {
    id: 3,
    name: "Elite Fitness Center",
    category: "Health & Fitness",
    rating: 4.7,
    reviews: 189,
    location: "Brooklyn, NY",
    image: "💪",
    isVerified: true,
  },
];

export default function FavouritesPage() {
  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">My Favorites</h1>
          <Button variant="outline" className="rounded-xl">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {trendingVendors.map((vendor) => (
            <Card
              key={vendor.id}
              className="p-4 hover:shadow-lg transition-shadow cursor-pointer rounded-2xl"
            >
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="text-3xl">{vendor.image}</div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-semibold flex items-center gap-2">
                          {vendor.name}
                          {vendor.isVerified && (
                            <Badge
                              variant="secondary"
                              className="text-xs bg-green-100 text-green-800"
                            >
                              Verified
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {vendor.category}
                        </div>
                        <div className="flex items-center gap-1 mt-1">
                          <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium">
                            {vendor.rating}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            ({vendor.reviews})
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-1 text-red-500"
                      >
                        <Heart className="w-4 h-4 fill-current" />
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="w-3 h-3" />
                  <span>{vendor.location}</span>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" className="flex-1 rounded-lg">
                    Contact
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 rounded-lg"
                  >
                    Remove
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
