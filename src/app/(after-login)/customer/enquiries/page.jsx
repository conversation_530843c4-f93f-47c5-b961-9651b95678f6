import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import React from "react";

const recentEnquiries = [
  {
    id: 1,
    vendor: "<PERSON>'s Italian Kitchen",
    message: "Catering for 50 people event",
    status: "responded",
    date: "2 days ago",
  },
  {
    id: 2,
    vendor: "TechFix Solutions",
    message: "MacBook repair service",
    status: "pending",
    date: "1 week ago",
  },
];

export default function EnquiriesPage() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">My Enquiries</h1>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            New Enquiry
          </Button>
        </div>

        <div className="space-y-4">
          {recentEnquiries.map((enquiry) => (
            <Card key={enquiry.id} className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold">{enquiry.vendor}</h3>
                    <p className="text-sm text-muted-foreground">
                      {enquiry.date}
                    </p>
                  </div>
                  <Badge
                    variant={
                      enquiry.status === "responded" ? "default" : "secondary"
                    }
                    className={
                      enquiry.status === "responded"
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {enquiry.status}
                  </Badge>
                </div>

                <p className="text-foreground">{enquiry.message}</p>

                <div className="flex gap-2">
                  <Button size="sm" className="rounded-lg">
                    View Details
                  </Button>
                  <Button variant="outline" size="sm" className="rounded-lg">
                    Follow Up
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
