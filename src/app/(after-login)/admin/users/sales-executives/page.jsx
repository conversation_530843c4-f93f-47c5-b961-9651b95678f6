"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Search } from "lucide-react";
import { salesExecutives as seConst } from "@/constants/customers";

export default function SalesExecutivesPage() {
  const [items, setItems] = useState(seConst);
  const [query, setQuery] = useState("");
  const [status, setStatus] = useState("all");
  const [region, setRegion] = useState("all");
  const [sort, setSort] = useState("name_asc");
  const [page, setPage] = useState(1);
  const pageSize = 8;

  const regions = Array.from(new Set(items.map((i) => i.region)));

  const filtered = useMemo(() => {
    let data = [...items];
    if (query)
      data = data.filter((s) =>
        [s.name, s.email, s.phone, s.region]
          .join(" ")
          .toLowerCase()
          .includes(query.toLowerCase())
      );
    if (status !== "all") data = data.filter((s) => s.status === status);
    if (region !== "all") data = data.filter((s) => s.region === region);
    switch (sort) {
      case "onboarded_desc":
        data.sort((a, b) => b.vendorsOnboarded - a.vendorsOnboarded);
        break;
      case "onboarded_asc":
        data.sort((a, b) => a.vendorsOnboarded - b.vendorsOnboarded);
        break;
      case "name_desc":
        data.sort((a, b) => b.name.localeCompare(a.name));
        break;
      default:
        data.sort((a, b) => a.name.localeCompare(b.name));
    }
    return data;
  }, [items, query, status, region, sort]);

  const pageCount = Math.max(1, Math.ceil(filtered.length / pageSize));
  const pageItems = filtered.slice((page - 1) * pageSize, page * pageSize);

  const [active, setActive] = useState(null);
  const [dialog, setDialog] = useState(null); // 'view' | 'edit' | 'disable'

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between gap-3">
        <h1 className="text-2xl font-bold">Sales Executives</h1>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <div className="relative flex-1 sm:w-72">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              className="pl-9 rounded-xl"
              placeholder="Search executives..."
              value={query}
              onChange={(e) => {
                setQuery(e.target.value);
                setPage(1);
              }}
            />
          </div>
          <Select
            value={status}
            onValueChange={(v) => {
              setStatus(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={region}
            onValueChange={(v) => {
              setRegion(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue placeholder="Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              {regions.map((r) => (
                <SelectItem key={r} value={r}>
                  {r}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={sort} onValueChange={setSort}>
            <SelectTrigger className="w-44 rounded-xl">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name_asc">Name (A-Z)</SelectItem>
              <SelectItem value="name_desc">Name (Z-A)</SelectItem>
              <SelectItem value="onboarded_desc">
                Onboarded (High-Low)
              </SelectItem>
              <SelectItem value="onboarded_asc">
                Onboarded (Low-High)
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card className="p-0 rounded-2xl overflow-hidden">
        <div className="grid grid-cols-7 gap-0 text-sm font-medium px-4 py-3 bg-muted/50">
          <div>Name</div>
          <div>Email</div>
          <div>Phone</div>
          <div>Region</div>
          <div>Onboarded</div>
          <div>Status</div>
          <div className="text-right">Actions</div>
        </div>
        <div className="divide-y">
          {pageItems.map((s) => (
            <div
              key={s.id}
              className="grid grid-cols-7 px-4 py-3 items-center text-sm"
            >
              <div className="font-medium">{s.name}</div>
              <div className="text-muted-foreground truncate">{s.email}</div>
              <div>{s.phone}</div>
              <div>{s.region}</div>
              <div>{s.vendorsOnboarded}</div>
              <div>
                <Badge className="capitalize">{s.status}</Badge>
              </div>
              <div className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="outline" className="rounded-lg">
                      Actions
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => {
                        setActive(s);
                        setDialog("view");
                      }}
                    >
                      View
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setActive(s);
                        setDialog("edit");
                      }}
                    >
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setActive(s);
                        setDialog("disable");
                      }}
                    >
                      {s.status === "active" ? "Disable" : "Enable"}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </Card>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => setPage((p) => Math.max(1, p - 1))}
            />
          </PaginationItem>
          {Array.from({ length: pageCount }).map((_, i) => (
            <PaginationItem key={i}>
              <PaginationLink
                isActive={page === i + 1}
                onClick={() => setPage(i + 1)}
              >
                {i + 1}
              </PaginationLink>
            </PaginationItem>
          ))}
          <PaginationItem>
            <PaginationNext
              onClick={() => setPage((p) => Math.min(pageCount, p + 1))}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>

      <Dialog open={!!dialog} onOpenChange={(o) => !o && setDialog(null)}>
        {dialog === "view" && active && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Executive Details</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-muted-foreground">Name</span>
                <div>{active.name}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Email</span>
                <div>{active.email}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Phone</span>
                <div>{active.phone}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Region</span>
                <div>{active.region}</div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setDialog(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        )}
        {dialog === "edit" && active && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Executive</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-3">
              <Input defaultValue={active.name} />
              <Input defaultValue={active.email} />
              <Input defaultValue={active.phone} />
              <Input defaultValue={active.region} />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDialog(null)}>
                Cancel
              </Button>
              <Button onClick={() => setDialog(null)}>Save</Button>
            </DialogFooter>
          </DialogContent>
        )}
        {dialog === "disable" && active && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {active.status === "active" ? "Disable" : "Enable"}{" "}
                {active.name}?
              </DialogTitle>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDialog(null)}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setItems((prev) =>
                    prev.map((it) =>
                      it.id === active.id
                        ? {
                            ...it,
                            status:
                              it.status === "active" ? "inactive" : "active",
                          }
                        : it
                    )
                  );
                  setDialog(null);
                }}
              >
                Confirm
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
}
