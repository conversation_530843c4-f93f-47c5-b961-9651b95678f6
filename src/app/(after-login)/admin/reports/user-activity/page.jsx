"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { userActivityReport } from "@/constants/adminReports";

export default function UserActivityReportPage() {
  const { totals, recent } = userActivityReport;
  const [query, setQuery] = useState("");
  const [sort, setSort] = useState("time_desc");
  const [range, setRange] = useState("this_week");
  const [page, setPage] = useState(1);
  const pageSize = 12;

  const rows = useMemo(() => {
    let data = [...recent];
    if (query)
      data = data.filter((r) =>
        [r.user, r.action, r.time]
          .join(" ")
          .toLowerCase()
          .includes(query.toLowerCase())
      );
    switch (sort) {
      case "user_asc":
        data.sort((a, b) => a.user.localeCompare(b.user));
        break;
      case "user_desc":
        data.sort((a, b) => b.user.localeCompare(a.user));
        break;
      default:
        data.sort((a, b) => b.time.localeCompare(a.time));
    }
    return data;
  }, [recent, query, sort]);
  const pageCount = Math.max(1, Math.ceil(rows.length / pageSize));
  const pageRows = rows.slice((page - 1) * pageSize, page * pageSize);

  const exportCsv = () => {
    const header = ["User", "Action", "Time"];
    const lines = rows.map((r) => [r.user, r.action, r.time].join(","));
    const blob = new Blob([[header.join(",")].concat(lines).join("\n")], {
      type: "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "user_activity.csv";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between gap-3">
        <h1 className="text-2xl font-bold">User Activity</h1>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <Input
            placeholder="Search users or actions..."
            className="rounded-xl sm:w-72"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setPage(1);
            }}
          />
          <Select
            value={range}
            onValueChange={(v) => {
              setRange(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue placeholder="Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="this_week">This Week</SelectItem>
              <SelectItem value="this_month">This Month</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={sort}
            onValueChange={(v) => {
              setSort(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="time_desc">Time (Newest)</SelectItem>
              <SelectItem value="user_asc">User (A-Z)</SelectItem>
              <SelectItem value="user_desc">User (Z-A)</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="rounded-xl" onClick={exportCsv}>
            Export CSV
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 rounded-2xl">
          <div className="text-sm text-muted-foreground">Logins</div>
          <div className="text-2xl font-semibold">{totals.logins}</div>
        </Card>
        <Card className="p-4 rounded-2xl">
          <div className="text-sm text-muted-foreground">Actions</div>
          <div className="text-2xl font-semibold">{totals.actions}</div>
        </Card>
        <Card className="p-4 rounded-2xl">
          <div className="text-sm text-muted-foreground">Failures</div>
          <div className="text-2xl font-semibold">{totals.failures}</div>
        </Card>
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {pageRows.length === 0 ? (
            <div className="p-6 text-center text-sm text-muted-foreground">
              No activity found.
            </div>
          ) : (
            pageRows.map((r) => (
              <div
                key={r.id}
                className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
              >
                <div>
                  <div className="font-medium">{r.user}</div>
                  <div className="text-sm text-muted-foreground">
                    {r.action}
                  </div>
                </div>
                <Badge variant="outline">{r.time}</Badge>
              </div>
            ))
          )}
        </div>
      </Card>
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => setPage((p) => Math.max(1, p - 1))}
            />
          </PaginationItem>
          {Array.from({ length: pageCount }).map((_, i) => (
            <PaginationItem key={i}>
              <PaginationLink
                isActive={page === i + 1}
                onClick={() => setPage(i + 1)}
              >
                {i + 1}
              </PaginationLink>
            </PaginationItem>
          ))}
          <PaginationItem>
            <PaginationNext
              onClick={() => setPage((p) => Math.min(pageCount, p + 1))}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
