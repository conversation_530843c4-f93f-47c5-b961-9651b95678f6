"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { revenueReport } from "@/constants/adminReports";

export default function RevenueReportPage() {
  const { summary, byPlan } = revenueReport;
  const [query, setQuery] = useState("");
  const [sort, setSort] = useState("revenue_desc");
  const [range, setRange] = useState("this_month");

  const rows = useMemo(() => {
    let data = [...byPlan];
    if (query)
      data = data.filter((r) =>
        r.plan.toLowerCase().includes(query.toLowerCase())
      );
    switch (sort) {
      case "revenue_asc":
        data.sort((a, b) => a.revenue - b.revenue);
        break;
      case "count_desc":
        data.sort((a, b) => b.count - a.count);
        break;
      case "count_asc":
        data.sort((a, b) => a.count - b.count);
        break;
      default:
        data.sort((a, b) => b.revenue - a.revenue);
    }
    return data;
  }, [byPlan, query, sort]);

  const exportCsv = () => {
    const header = ["Plan", "Subscriptions", "Revenue"];
    const lines = rows.map((r) => `${r.plan},${r.count},${r.revenue}`);
    const blob = new Blob([[header.join(",")].concat(lines).join("\n")], {
      type: "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `revenue_by_plan.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between gap-3">
        <h1 className="text-2xl font-bold">Revenue</h1>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <Input
            placeholder="Search plans..."
            className="rounded-xl sm:w-64"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <Select value={range} onValueChange={setRange}>
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue placeholder="Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="this_week">This Week</SelectItem>
              <SelectItem value="this_month">This Month</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sort} onValueChange={setSort}>
            <SelectTrigger className="w-44 rounded-xl">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue_desc">Revenue (High-Low)</SelectItem>
              <SelectItem value="revenue_asc">Revenue (Low-High)</SelectItem>
              <SelectItem value="count_desc">
                Subscriptions (High-Low)
              </SelectItem>
              <SelectItem value="count_asc">
                Subscriptions (Low-High)
              </SelectItem>
            </SelectContent>
          </Select>
          <Button className="rounded-xl" onClick={exportCsv}>
            Export CSV
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 rounded-2xl">
          <div className="text-sm text-muted-foreground">Today</div>
          <div className="text-2xl font-semibold">${summary.today}</div>
        </Card>
        <Card className="p-4 rounded-2xl">
          <div className="text-sm text-muted-foreground">This Week</div>
          <div className="text-2xl font-semibold">${summary.thisWeek}</div>
        </Card>
        <Card className="p-4 rounded-2xl">
          <div className="text-sm text-muted-foreground">This Month</div>
          <div className="text-2xl font-semibold">${summary.thisMonth}</div>
        </Card>
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">By Plan</h3>
        <div className="space-y-3">
          {rows.map((p) => (
            <div
              key={p.plan}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <div>
                <div className="font-medium">{p.plan}</div>
                <div className="text-sm text-muted-foreground">
                  {p.count} subscriptions
                </div>
              </div>
              <Badge>${p.revenue}</Badge>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
