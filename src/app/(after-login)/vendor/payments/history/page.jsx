import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditCard } from "lucide-react";

export default function Page() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Payments & Subscription</h1>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            <CreditCard className="w-4 h-4 mr-2" />
            Upgrade Plan
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold mb-4">Current Plan</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Plan Type</span>
                <Badge className="bg-primary text-white">Premium</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Monthly Fee</span>
                <span className="font-semibold">$99/month</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Next Billing</span>
                <span className="text-sm">Feb 15, 2024</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Status</span>
                <Badge className="bg-green-100 text-green-800">Active</Badge>
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold mb-4">Payment History</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-muted/30 rounded-xl">
                <div>
                  <span className="font-medium">Jan 2024</span>
                  <p className="text-xs text-muted-foreground">Premium Plan</p>
                </div>
                <span className="font-semibold">$99</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-muted/30 rounded-xl">
                <div>
                  <span className="font-medium">Dec 2023</span>
                  <p className="text-xs text-muted-foreground">Premium Plan</p>
                </div>
                <span className="font-semibold">$99</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
