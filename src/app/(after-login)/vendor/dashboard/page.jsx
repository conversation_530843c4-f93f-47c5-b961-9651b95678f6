"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  BarChart3,
  Eye,
  MessageSquare,
  Star,
  Calendar,
  CreditCard,
  Settings,
  Upload,
  MapPin,
  Clock,
  Phone,
  Mail,
  Globe,
  TrendingUp,
  DollarSign,
  Users,
  FileText,
} from "lucide-react";

export default function VendorDashboard() {
  // Mock data
  const businessStats = {
    views: 1240,
    enquiries: 45,
    reviews: 23,
    rating: 4.6,
  };

  const recentEnquiries = [
    {
      id: 1,
      customer: "<PERSON>",
      message: "Looking for catering services for a wedding",
      date: "2 hours ago",
      status: "new",
    },
    {
      id: 2,
      customer: "<PERSON>",
      message: "Need help with kitchen renovation",
      date: "5 hours ago",
      status: "contacted",
    },
    {
      id: 3,
      customer: "<PERSON>",
      message: "Interested in your weekend special menu",
      date: "1 day ago",
      status: "completed",
    },
  ];

  const reviews = [
    {
      id: 1,
      customer: "<PERSON>",
      rating: 5,
      comment: "Excellent service! Highly recommend.",
      date: "3 days ago",
    },
    {
      id: 2,
      customer: "John Smith",
      rating: 4,
      comment: "Good quality food and timely delivery.",
      date: "1 week ago",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <main className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Dashboard</h1>
              <p className="text-muted-foreground">
                Welcome back! Here's your business overview.
              </p>
            </div>
            <Button className="rounded-xl bg-primary hover:bg-primary/90">
              <TrendingUp className="w-4 h-4 mr-2" />
              View Analytics
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Eye className="w-5 h-5 text-primary" />
                  <span className="text-sm text-green-600 font-medium">
                    +12%
                  </span>
                </div>
                <div className="text-2xl font-bold">{businessStats.views}</div>
                <div className="text-sm text-muted-foreground">
                  Profile Views
                </div>
              </div>
            </Card>

            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <MessageSquare className="w-5 h-5 text-primary" />
                  <span className="text-sm text-green-600 font-medium">
                    +8%
                  </span>
                </div>
                <div className="text-2xl font-bold">
                  {businessStats.enquiries}
                </div>
                <div className="text-sm text-muted-foreground">Enquiries</div>
              </div>
            </Card>

            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Star className="w-5 h-5 text-yellow-500" />
                  <span className="text-sm text-green-600 font-medium">
                    +0.2
                  </span>
                </div>
                <div className="text-2xl font-bold">{businessStats.rating}</div>
                <div className="text-sm text-muted-foreground">Rating</div>
              </div>
            </Card>

            <Card className="p-4 rounded-2xl">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Users className="w-5 h-5 text-blue-500" />
                  <span className="text-sm text-green-600 font-medium">
                    +15%
                  </span>
                </div>
                <div className="text-2xl font-bold">
                  {businessStats.reviews}
                </div>
                <div className="text-sm text-muted-foreground">Reviews</div>
              </div>
            </Card>
          </div>

          {/* Recent Activities */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Recent Enquiries</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentView("enquiries")}
                  >
                    View All
                  </Button>
                </div>
                <div className="space-y-4">
                  {recentEnquiries.slice(0, 3).map((enquiry) => (
                    <div
                      key={enquiry.id}
                      className="flex items-start gap-3 p-3 bg-muted/30 rounded-xl"
                    >
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="text-xs">
                          {enquiry.customer
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-sm">
                            {enquiry.customer}
                          </span>
                          <Badge
                            variant={
                              enquiry.status === "new" ? "default" : "secondary"
                            }
                            className={`text-xs ${
                              enquiry.status === "new"
                                ? "bg-accent text-white"
                                : enquiry.status === "contacted"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-green-100 text-green-800"
                            }`}
                          >
                            {enquiry.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-1">
                          {enquiry.message}
                        </p>
                        <span className="text-xs text-muted-foreground">
                          {enquiry.date}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            <Card className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Recent Reviews</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentView("reviews")}
                  >
                    View All
                  </Button>
                </div>
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="p-3 bg-muted/30 rounded-xl">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">
                          {review.customer}
                        </span>
                        <div className="flex items-center gap-1">
                          {Array.from({ length: 5 }, (_, i) => (
                            <Star
                              key={i}
                              className={`w-3 h-3 ${
                                i < review.rating
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "text-muted-foreground"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">
                        {review.comment}
                      </p>
                      <span className="text-xs text-muted-foreground">
                        {review.date}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
