import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import React from "react";

const recentEnquiries = [
  {
    id: 1,
    customer: "<PERSON>",
    message: "Looking for catering services for a wedding",
    date: "2 hours ago",
    status: "new",
  },
  {
    id: 2,
    customer: "<PERSON>",
    message: "Need help with kitchen renovation",
    date: "5 hours ago",
    status: "contacted",
  },
  {
    id: 3,
    customer: "<PERSON>",
    message: "Interested in your weekend special menu",
    date: "1 day ago",
    status: "completed",
  },
];

export default function Page() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Enquiries</h1>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            Export List
          </Button>
        </div>

        <div className="space-y-4">
          {recentEnquiries.map((enquiry) => (
            <Card key={enquiry.id} className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <Avatar>
                      <AvatarFallback>
                        {enquiry.customer
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{enquiry.customer}</h3>
                      <p className="text-xs text-muted-foreground">
                        {enquiry.date}
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant={enquiry.status === "new" ? "default" : "secondary"}
                    className={
                      enquiry.status === "new"
                        ? "bg-accent text-white"
                        : enquiry.status === "contacted"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-green-100 text-green-800"
                    }
                  >
                    {enquiry.status}
                  </Badge>
                </div>

                <p className="text-foreground">{enquiry.message}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
