import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { engagementReport } from "@/constants/reports";

export default function EngagementReportPage() {
  const { metrics, notifications } = engagementReport;
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Engagement</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {metrics.map((m) => (
          <Card
            key={m.name}
            className="p-4 rounded-2xl flex items-center justify-between"
          >
            <div className="text-sm text-muted-foreground">{m.name}</div>
            <div className="text-xl font-semibold">{m.value}</div>
          </Card>
        ))}
      </div>
      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {notifications.map((n) => (
            <div
              key={n.id}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <div>{n.title}</div>
              <Badge variant="outline">{n.time}</Badge>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
