import { Card } from "@/components/ui/card";
import { vendorsOnboardingReport } from "@/constants/reports";

export default function VendorsOnboardingReportPage() {
  const { series } = vendorsOnboardingReport;
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Vendors Onboarding</h1>
      <Card className="p-6 rounded-2xl">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {series.map((d) => (
            <div key={d.day} className="p-4 bg-muted/30 rounded-xl text-center">
              <div className="text-sm text-muted-foreground">{d.day}</div>
              <div className="text-2xl font-semibold">{d.onboarded}</div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
