import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { plansPaymentsReport } from "@/constants/reports";

export default function PlansPaymentsReportPage() {
  const { payments, totals } = plansPaymentsReport;
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Plans & Payments</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 rounded-2xl">
          Paid: <span className="font-semibold">${totals.paid}</span>
        </Card>
        <Card className="p-4 rounded-2xl">
          Failed: <span className="font-semibold">${totals.failed}</span>
        </Card>
        <Card className="p-4 rounded-2xl">
          Refunded: <span className="font-semibold">${totals.refunded}</span>
        </Card>
      </div>
      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Recent Payments</h3>
        <div className="space-y-3">
          {payments.map((p) => (
            <div
              key={p.id}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <div>
                <div className="font-medium">{p.vendor}</div>
                <div className="text-sm text-muted-foreground">
                  Plan: {p.plan} • {p.date}
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="font-semibold">${p.amount}</div>
                <Badge className="capitalize">{p.status}</Badge>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
