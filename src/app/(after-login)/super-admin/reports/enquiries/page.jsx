import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { enquiriesReport } from "@/constants/reports";

export default function EnquiriesReportPage() {
  const { statusCounts, daily, topCategories } = enquiriesReport;
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Enquiries Report</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {statusCounts.map((s) => (
          <Card
            key={s.status}
            className="p-4 rounded-2xl flex items-center justify-between"
          >
            <div className="font-medium capitalize">{s.status}</div>
            <Badge className="text-base">{s.count}</Badge>
          </Card>
        ))}
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Top Categories</h3>
        <div className="space-y-2">
          {topCategories.map((c) => (
            <div
              key={c.name}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <span>{c.name}</span>
              <Badge variant="outline">{c.enquiries} enquiries</Badge>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
