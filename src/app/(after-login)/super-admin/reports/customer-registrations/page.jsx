"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { customerRegistrationsReport } from "@/constants/adminReports";

export default function CustomerRegistrationsReportPage() {
  const { weekly, total } = customerRegistrationsReport;
  const [range, setRange] = useState("this_week");

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between gap-3">
        <h1 className="text-2xl font-bold">Customer Registrations</h1>
        <Select value={range} onValueChange={setRange}>
          <SelectTrigger className="w-40 rounded-xl">
            <SelectValue placeholder="Range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="this_week">This Week</SelectItem>
            <SelectItem value="last_week">Last Week</SelectItem>
            <SelectItem value="this_month">This Month</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 rounded-2xl flex items-center justify-between">
          <div className="text-sm text-muted-foreground">Total (This Week)</div>
          <div className="text-xl font-semibold">{total}</div>
        </Card>
      </div>
      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Weekly Breakdown</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
          {weekly.map((d) => (
            <div key={d.day} className="p-4 bg-muted/30 rounded-xl text-center">
              <div className="text-sm text-muted-foreground">{d.day}</div>
              <div className="text-2xl font-semibold">{d.customers}</div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
