import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { paymentsHistory } from "@/constants/sales";

export default function SalesPaymentsHistoryPage() {
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Payments History</h1>
      <Card className="p-6 rounded-2xl">
        <div className="space-y-3">
          {paymentsHistory.map((p) => (
            <div
              key={p.id}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <div>
                <div className="font-medium">{p.vendor}</div>
                <div className="text-sm text-muted-foreground">
                  Plan: {p.plan} • {p.date}
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="font-semibold">${p.amount}</div>
                <Badge className="capitalize">{p.status}</Badge>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
