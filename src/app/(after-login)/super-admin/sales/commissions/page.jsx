import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { salesCommissions } from "@/constants/sales";

export default function SalesCommissionsPage() {
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Sales Commissions</h1>
      <Card className="p-6 rounded-2xl">
        <div className="space-y-3">
          {salesCommissions.map((c) => (
            <div
              key={c.id}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <div>
                <div className="font-medium">{c.vendor}</div>
                <div className="text-sm text-muted-foreground">{c.date}</div>
              </div>
              <div className="flex items-center gap-3">
                <div className="font-semibold">${c.amount}</div>
                <Badge className="capitalize">{c.status}</Badge>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
