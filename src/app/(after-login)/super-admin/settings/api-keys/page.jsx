"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { apiKeys as apiKeyConstants } from "@/constants/settings";

export default function ApiKeysSettingsPage() {
  const [items, setItems] = useState(apiKeyConstants);
  const [open, setOpen] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [form, setForm] = useState({
    provider: "Razorpay",
    account: "",
    publicKey: "",
    secret: "",
    status: "connected",
  });
  const providers = ["Razorpay", "Stripe", "Twilio"];

  const openCreate = () => {
    setEditingId(null);
    setForm({
      provider: "Razorpay",
      account: "",
      publicKey: "",
      secret: "",
      status: "connected",
    });
    setOpen(true);
  };

  const openEdit = (item) => {
    setEditingId(item.id);
    setForm({
      provider: item.provider,
      account: item.account,
      publicKey: item.publicKey,
      secret: item.secret,
      status: item.status,
    });
    setOpen(true);
  };

  const save = () => {
    if (editingId) {
      setItems((prev) =>
        prev.map((i) => (i.id === editingId ? { ...i, ...form } : i))
      );
    } else {
      const nextId = (items[0]?.id || 0) + 1;
      setItems((prev) => [{ id: nextId, ...form, lastSynced: "—" }, ...prev]);
    }
    setOpen(false);
  };

  const copy = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch {}
  };

  const toggleConnection = (id) => {
    setItems((prev) =>
      prev.map((i) =>
        i.id === id
          ? {
              ...i,
              status: i.status === "connected" ? "disconnected" : "connected",
            }
          : i
      )
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Third-Party API Keys</h1>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button className="rounded-xl" onClick={openCreate}>
              Connect Provider
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingId ? "Edit Provider" : "Connect Provider"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm">Provider</label>
                <Select
                  value={form.provider}
                  onValueChange={(v) => setForm({ ...form, provider: v })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Choose provider" />
                  </SelectTrigger>
                  <SelectContent>
                    {providers.map((p) => (
                      <SelectItem key={p} value={p}>
                        {p}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm">Account</label>
                <Input
                  className="mt-1"
                  value={form.account}
                  onChange={(e) =>
                    setForm({ ...form, account: e.target.value })
                  }
                  placeholder="e.g., Primary, EU, Sandbox"
                />
              </div>
              <div>
                <label className="text-sm">Public Key / ID</label>
                <Input
                  className="mt-1 font-mono"
                  value={form.publicKey}
                  onChange={(e) =>
                    setForm({ ...form, publicKey: e.target.value })
                  }
                  placeholder="rzp_live_xxx or pk_live_xxx or ACxxxx"
                />
              </div>
              <div>
                <label className="text-sm">Secret</label>
                <Input
                  className="mt-1 font-mono"
                  type="password"
                  value={form.secret}
                  onChange={(e) => setForm({ ...form, secret: e.target.value })}
                  placeholder="Secret/token from provider dashboard"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={save}>{editingId ? "Save" : "Connect"}</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <Card className="p-0 rounded-2xl overflow-hidden">
        <div className="grid grid-cols-6 gap-0 text-sm font-medium px-4 py-3 bg-muted/50">
          <div>Provider</div>
          <div>Account</div>
          <div>Public Key</div>
          <div>Last Synced</div>
          <div>Status</div>
          <div className="text-right">Actions</div>
        </div>
        <div className="divide-y">
          {items.map((k) => (
            <div
              key={k.id}
              className="grid grid-cols-6 px-4 py-3 items-center text-sm"
            >
              <div className="font-medium">{k.provider}</div>
              <div>{k.account}</div>
              <div className="font-mono text-xs truncate">{k.publicKey}</div>
              <div>{k.lastSynced || ""}</div>
              <div>
                <Badge className="capitalize">{k.status}</Badge>
              </div>
              <div className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="outline" className="rounded-lg">
                      Manage
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>{k.provider}</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => openEdit(k)}>
                      Edit configuration
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => copy(k.publicKey)}>
                      Copy public key
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => copy(k.secret)}>
                      Copy secret
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => toggleConnection(k.id)}>
                      {k.status === "connected" ? "Disconnect" : "Connect"}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
