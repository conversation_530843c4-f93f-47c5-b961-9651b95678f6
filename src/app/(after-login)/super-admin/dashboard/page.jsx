"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  Users,
  Building2,
  DollarSign,
  Settings,
  UserPlus,
  FileText,
  Shield,
  Download,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import {
  CategoryChart,
  ChartCard,
  EnquiryChart,
} from "@/components/blocks/DashboardCharts";
// import { MobileBottomNav } from "../layout/MobileBottomNav";
// import { DesktopSidebar } from "../layout/DesktopSidebar";
import { systemStats, recentActivities } from "@/constants/dashboards";

export default function SuperAdminDashboard() {
  const pendingApprovals = [
    {
      id: 1,
      vendor: "TechFix Solutions",
      category: "Technology",
      registeredBy: "<PERSON> (Sales Exec)",
      date: "2024-01-15",
      status: "pending",
    },
    {
      id: 2,
      vendor: "Green Garden Landscaping",
      category: "Services",
      registeredBy: "Lisa Brown (Sales Exec)",
      date: "2024-01-14",
      status: "pending",
    },
  ];

  return (
    <div className="min-h-screen bg-background max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">System Overview</h1>
            <p className="text-muted-foreground">
              Monitor platform performance and manage system-wide operations
            </p>
          </div>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            <Download className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="p-4 rounded-2xl">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Building2 className="w-5 h-5 text-primary" />
                <span className="text-sm text-green-600 font-medium">
                  +{systemStats.monthlyGrowth.vendors}%
                </span>
              </div>
              <div className="text-2xl font-bold">
                {systemStats.totalVendors.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Total Vendors</div>
            </div>
          </Card>

          <Card className="p-4 rounded-2xl">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Users className="w-5 h-5 text-blue-500" />
                <span className="text-sm text-green-600 font-medium">
                  +{systemStats.monthlyGrowth.customers}%
                </span>
              </div>
              <div className="text-2xl font-bold">
                {systemStats.totalCustomers.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">
                Total Customers
              </div>
            </div>
          </Card>

          <Card className="p-4 rounded-2xl">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Shield className="w-5 h-5 text-primary" />
                <span className="text-sm text-muted-foreground">Active</span>
              </div>
              <div className="text-2xl font-bold">
                {systemStats.totalSalesExecs}
              </div>
              <div className="text-sm text-muted-foreground">
                Sales Executives
              </div>
            </div>
          </Card>

          <Card className="p-4 rounded-2xl">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <DollarSign className="w-5 h-5 text-green-500" />
                <span className="text-sm text-green-600 font-medium">
                  +{systemStats.monthlyGrowth.revenue}%
                </span>
              </div>
              <div className="text-2xl font-bold">
                ${(systemStats.totalRevenue / 1000).toFixed(0)}K
              </div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
            </div>
          </Card>
        </div>

        {/* Analytics Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartCard
            title="Your Enquiry Activity"
            subtitle="Track your vendor interactions"
          >
            <EnquiryChart />
          </ChartCard>

          <ChartCard
            title="Popular Categories"
            subtitle="Top vendor categories in your area"
          >
            <CategoryChart />
          </ChartCard>
        </div>

        {/* Recent Activities & Pending Approvals */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Recent Activities</h3>
                <Button variant="ghost" size="sm">
                  View All
                </Button>
              </div>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start gap-3 p-3 bg-muted/30 rounded-xl"
                  >
                    <div
                      className={`w-2 h-2 rounded-full mt-2 ${
                        activity.status === "completed"
                          ? "bg-green-500"
                          : activity.status === "pending_approval"
                          ? "bg-yellow-500"
                          : "bg-blue-500"
                      }`}
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.message}</p>
                      <span className="text-xs text-muted-foreground">
                        {activity.time}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Pending Approvals</h3>
                <Badge
                  variant="secondary"
                  className="bg-yellow-100 text-yellow-800"
                >
                  {pendingApprovals.length} pending
                </Badge>
              </div>
              <div className="space-y-4">
                {pendingApprovals.map((vendor) => (
                  <div key={vendor.id} className="p-3 bg-muted/30 rounded-xl">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-semibold text-sm">
                          {vendor.vendor}
                        </h4>
                        <p className="text-xs text-muted-foreground">
                          {vendor.category}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          By {vendor.registeredBy}
                        </p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        <Clock className="w-3 h-3 mr-1" />
                        {vendor.date}
                      </Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="h-7 text-xs rounded-lg bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Approve
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 text-xs rounded-lg"
                      >
                        <XCircle className="w-3 h-3 mr-1" />
                        Reject
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
// {/*
//       <MobileBottomNav navigationItems={navigationItems} currentView={currentView} onNavigate={setCurrentView} />
//       */}
