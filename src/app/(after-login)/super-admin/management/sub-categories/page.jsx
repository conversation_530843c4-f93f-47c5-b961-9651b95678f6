import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Edit, Plus } from "lucide-react";
import React from "react";
import { subCategories } from "@/constants/categories";

export default function SubCategoriesPage() {
  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Sub-Category Management</h1>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Add Sub-Category
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {subCategories.map((subCategory) => (
          <Card key={subCategory.id} className="p-4 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{subCategory.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {subCategory.count} vendors
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={subCategory.isActive ? "outline" : "secondary"}>
                  {subCategory.category.name}
                </Badge>
                <Button variant="ghost" size="sm">
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
