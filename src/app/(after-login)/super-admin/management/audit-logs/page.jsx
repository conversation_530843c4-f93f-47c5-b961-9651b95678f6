"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Search } from "lucide-react";
import { auditLogs as logsConst } from "@/constants/auditLogs";

export default function AuditLogsPage() {
  const [items] = useState(logsConst);
  const [query, setQuery] = useState("");
  const [status, setStatus] = useState("all");
  const [module, setModule] = useState("all");
  const [page, setPage] = useState(1);
  const [sort, setSort] = useState("time_desc");
  const pageSize = 12;

  const modules = Array.from(new Set(items.map((i) => i.module)));

  const filtered = useMemo(() => {
    let data = [...items];
    if (query)
      data = data.filter((l) =>
        [l.actor, l.action, l.target, l.module]
          .join(" ")
          .toLowerCase()
          .includes(query.toLowerCase())
      );
    if (status !== "all") data = data.filter((l) => l.status === status);
    if (module !== "all") data = data.filter((l) => l.module === module);
    switch (sort) {
      case "time_asc":
        data.sort((a, b) => String(a.time).localeCompare(String(b.time)));
        break;
      case "actor_asc":
        data.sort((a, b) => a.actor.localeCompare(b.actor));
        break;
      case "actor_desc":
        data.sort((a, b) => b.actor.localeCompare(a.actor));
        break;
      default:
        data.sort((a, b) => String(b.time).localeCompare(String(a.time)));
    }
    return data;
  }, [items, query, status, module, sort]);

  const pageCount = Math.max(1, Math.ceil(filtered.length / pageSize));
  const pageItems = filtered.slice((page - 1) * pageSize, page * pageSize);

  const exportCsv = () => {
    const header = ["Time", "Actor", "Module", "Action", "Target", "Status"];
    const lines = filtered.map((l) =>
      [l.time, l.actor, l.module, l.action, l.target, l.status].join(",")
    );
    const blob = new Blob([[header.join(",")].concat(lines).join("\n")], {
      type: "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "audit_logs.csv";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between gap-3">
        <h1 className="text-2xl font-bold">Audit Logs</h1>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <div className="relative flex-1 sm:w-80">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              className="pl-9 rounded-xl"
              placeholder="Search logs..."
              value={query}
              onChange={(e) => {
                setQuery(e.target.value);
                setPage(1);
              }}
            />
          </div>
          <Select
            value={module}
            onValueChange={(v) => {
              setModule(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-44 rounded-xl">
              <SelectValue placeholder="Module" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Modules</SelectItem>

              {modules.map((m) => (
                <SelectItem key={m} value={m}>
                  {m}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select
            value={status}
            onValueChange={(v) => {
              setStatus(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="success">Success</SelectItem>
              <SelectItem value="failure">Failure</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={sort}
            onValueChange={(v) => {
              setSort(v);
              setPage(1);
            }}
          >
            <SelectTrigger className="w-44 rounded-xl">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="time_desc">Time (Newest)</SelectItem>
              <SelectItem value="time_asc">Time (Oldest)</SelectItem>
              <SelectItem value="actor_asc">Actor (A-Z)</SelectItem>
              <SelectItem value="actor_desc">Actor (Z-A)</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="rounded-xl" onClick={exportCsv}>
            Export CSV
          </Button>
        </div>
      </div>

      <Card className="p-0 rounded-2xl overflow-hidden">
        <div className="grid grid-cols-7 gap-0 text-sm font-medium px-4 py-3 bg-muted/50">
          <div>Time</div>
          <div>Actor</div>
          <div>Module</div>

          <div>Action</div>
          <div className="col-span-2">Target</div>
          <div>Status</div>
        </div>
        <div className="divide-y">
          {pageItems.length === 0 ? (
            <div className="p-8 text-center text-sm text-muted-foreground">
              No logs found. Try adjusting filters.
            </div>
          ) : (
            pageItems.map((l) => (
              <div
                key={l.id}
                className="grid grid-cols-7 px-4 py-3 items-center text-sm"
              >
                <div>{l.time}</div>
                <div>{l.actor}</div>
                <div>{l.module}</div>
                <div className="font-mono text-xs">{l.action}</div>
                <div className="col-span-2 truncate" title={l.target}>
                  {l.target}
                </div>
                <div>
                  <Badge
                    className="capitalize"
                    variant={l.status === "success" ? "outline" : undefined}
                  >
                    {l.status}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </div>
      </Card>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => setPage((p) => Math.max(1, p - 1))}
            />
          </PaginationItem>
          {Array.from({ length: pageCount }).map((_, i) => (
            <PaginationItem key={i}>
              <PaginationLink
                isActive={page === i + 1}
                onClick={() => setPage(i + 1)}
              >
                {i + 1}
              </PaginationLink>
            </PaginationItem>
          ))}
          <PaginationItem>
            <PaginationNext
              onClick={() => setPage((p) => Math.min(pageCount, p + 1))}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
