/**
 * Utility functions for handling user roles and permissions
 */

// Mock function to get user role - replace with actual authentication logic
export const getCurrentUserRole = () => {
  // This would typically come from your authentication system
  // For now, we'll return super-admin as default
  // In a real app, this might come from:
  // - JWT token
  // - Session storage
  // - API call to get current user
  // - Context/Redux store
  
  if (typeof window !== 'undefined') {
    // Check localStorage for demo purposes
    const storedRole = localStorage.getItem('userRole');
    if (storedRole) {
      return storedRole;
    }
  }
  
  return 'super-admin'; // Default role
};

// Function to set user role (for demo/testing purposes)
export const setCurrentUserRole = (role) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('userRole', role);
  }
};

// Function to check if user has specific permissions
export const hasPermission = (userRole, requiredRole) => {
  const roleHierarchy = {
    'super-admin': 4,
    'admin': 3,
    'sales-executive': 2,
    'vendor': 1,
    'customer': 1
  };
  
  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

// Function to get role display name
export const getRoleDisplayName = (role) => {
  const roleNames = {
    'super-admin': 'Super Administrator',
    'admin': 'Administrator',
    'sales-executive': 'Sales Executive',
    'vendor': 'Vendor',
    'customer': 'Customer'
  };
  
  return roleNames[role] || 'Unknown Role';
};

// Function to get role-based redirect URL
export const getRoleBasedRedirectUrl = (role) => {
  const redirectUrls = {
    'super-admin': '/super-admin/dashboard',
    'admin': '/admin/dashboard',
    'sales-executive': '/sales/dashboard',
    'vendor': '/vendor/dashboard',
    'customer': '/customer/dashboard'
  };
  
  return redirectUrls[role] || '/';
};
