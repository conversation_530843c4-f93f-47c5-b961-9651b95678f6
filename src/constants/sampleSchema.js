/*
Universal Sample Data Schema (v1)

This file documents shapes used by sample data constants.
Use these JSDoc typedefs as reference when adding data.
*/

/** @typedef {{id:number, name:string, count?:number, isActive?:boolean}} Category */
/** @typedef {{id:number, name:string, count?:number, category:{id:number,name:string}, isActive?:boolean}} SubCategory */

/**
 * @typedef Vendor
 * @property {number} id
 * @property {string} businessName
 * @property {string} contactPerson
 * @property {string} category
 * @property {string} location
 * @property {string=} onboardedDate
 * @property {"approved"|"pending"|"rejected"=} status
 * @property {number=} feeCollected
 * @property {number=} commission
 * @property {string=} registeredBy
 * @property {string=} submittedDate
 * @property {number=} documents
 * @property {"completed"|"pending"|"failed"=} paymentStatus
 */

/** @typedef {{vendorsOnboarded:number, feesCollected:number, commissionEarned:number, thisMonthTarget:number, commissionRate:number}} SalesStats */
/** @typedef {{activeVendors:number, enquiriesToday:number, vendorsOnboardedBySales:number, pendingApprovals:number}} AdminStats */
/** @typedef {{totalVendors:number, totalCustomers:number, totalSalesExecs:number, totalRevenue:number, monthlyGrowth:{vendors:number, customers:number, revenue:number}}} SystemStats */

/** @typedef {{id:number, customer:string, vendor:string, message:string, date:string, status:"new"|"responded"|"closed"}} Enquiry */
/** @typedef {{id:number, type:string, message:string, time:string, status:"completed"|"pending_approval"|"info"}} Activity */
/** @typedef {{id:number, location:string, purpose:string, date:string, time:string, status:"scheduled"|"completed", contact?:string, outcome?:string}} Visit */
/** @typedef {{id:number, name:string, email:string, role:string, status:string, lastLogin:string}} SystemUser */
/** @typedef {{name:string, category:string, description:string, services:string, address:string, phone:string, email:string, website:string, hours:Record<string,string>}} BusinessProfile */
/** @typedef {{id:number, plan:string, vendor:string, amount:number, date:string, status:"paid"|"failed"|"refunded"}} Payment */

export const SCHEMA_VERSION = "v1";

