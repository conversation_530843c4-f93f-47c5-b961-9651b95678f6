// Sales-related sample data

/** @type {import('./sampleSchema').SalesStats} */
export const salesStats = {
  vendorsOnboarded: 28,
  feesCollected: 2790,
  commissionEarned: 1395,
  thisMonthTarget: 35,
  commissionRate: 0.5,
};

/** @type {import('./sampleSchema').Vendor[]} */
export { recentVendors } from "./vendors";

/** @type {import('./sampleSchema').Visit[]} */
export const upcomingVisits = [
  {
    id: 1,
    location: "Downtown Brooklyn",
    purpose: "Initial vendor meeting",
    date: "2024-01-16",
    time: "10:30 AM",
    contact: "<PERSON>",
    status: "scheduled",
  },
  {
    id: 2,
    location: "Tech Hub, Manhattan",
    purpose: "Follow-up on document verification",
    date: "2024-01-16",
    time: "2:15 PM",
    contact: "<PERSON>",
    status: "scheduled",
  },
];

/** @type {import('./sampleSchema').Visit[]} */
export const completedVisits = [
  {
    id: 1,
    location: "Downtown Brooklyn",
    purpose: "Initial vendor meeting",
    date: "2024-01-15",
    time: "10:30 AM",
    outcome: "<PERSON>end<PERSON> signed up successfully",
    status: "completed",
  },
  {
    id: 2,
    location: "Tech Hub, Manhattan",
    purpose: "Follow-up on document verification",
    date: "2024-01-14",
    time: "2:15 PM",
    outcome: "Documents submitted",
    status: "completed",
  },
];

export const salesCommissions = [
  { id: 1, vendor: "Giuseppe's Italian Kitchen", amount: 49.5, date: "2024-01-15", status: "paid" },
  { id: 2, vendor: "TechFix Solutions", amount: 49.5, date: "2024-01-14", status: "pending" },
  { id: 3, vendor: "Green Garden Landscaping", amount: 49.5, date: "2024-01-12", status: "paid" },
];

export const paymentsHistory = [
  { id: 101, vendor: "Giuseppe's Italian Kitchen", plan: "Starter", amount: 99, date: "2024-01-15", status: "paid" },
  { id: 102, vendor: "TechFix Solutions", plan: "Starter", amount: 99, date: "2024-01-14", status: "paid" },
  { id: 103, vendor: "Green Garden Landscaping", plan: "Starter", amount: 99, date: "2024-01-12", status: "paid" },
];

