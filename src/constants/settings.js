// Third-Party API Providers (e.g., Razorpay, Stripe, <PERSON>wilio)
// This page manages external integrations, not internal app keys.

export const apiKeys = [
  {
    id: 1,
    provider: "Razorpay",
    account: "Primary",
    publicKey: "rzp_live_1a2b3c",
    secret: "rzp_secret_xxxxx",
    status: "connected",
    lastSynced: "2024-01-15",
  },
  {
    id: 2,
    provider: "Stripe",
    account: "EU",
    publicKey: "pk_live_abcd",
    secret: "sk_live_xxxxx",
    status: "disconnected",
    lastSynced: "—",
  },
  {
    id: 3,
    provider: "Twi<PERSON>",
    account: "SMS",
    publicKey: "ACxxxxxxxx",
    secret: "tw_secret_xxxxx",
    status: "connected",
    lastSynced: "2024-01-10",
  },
];
