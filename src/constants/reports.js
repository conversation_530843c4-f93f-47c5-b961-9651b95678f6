// Reports-oriented sample data

export const enquiriesReport = {
  statusCounts: [
    { status: "new", count: 58 },
    { status: "responded", count: 132 },
    { status: "closed", count: 84 },
  ],
  daily: [
    { day: "Mon", enquiries: 45 },
    { day: "Tue", enquiries: 52 },
    { day: "Wed", enquiries: 38 },
    { day: "Thu", enquiries: 64 },
    { day: "Fri", enquiries: 58 },
    { day: "Sat", enquiries: 42 },
    { day: "Sun", enquiries: 35 },
  ],
  topCategories: [
    { name: "Restaurants & Food", enquiries: 120 },
    { name: "Services", enquiries: 90 },
    { name: "Technology", enquiries: 70 },
  ],
};

export const plansPaymentsReport = {
  payments: [
    { id: 201, vendor: "Giuseppe's Italian Kitchen", plan: "Starter", amount: 99, date: "2024-01-15", status: "paid" },
    { id: 202, vendor: "TechFix Solutions", plan: "Starter", amount: 99, date: "2024-01-14", status: "paid" },
    { id: 203, vendor: "Green Garden Landscaping", plan: "Starter", amount: 99, date: "2024-01-12", status: "paid" },
  ],
  totals: { paid: 297, failed: 0, refunded: 0 },
};

export const vendorsOnboardingReport = {
  series: [
    { day: "Mon", onboarded: 4 },
    { day: "Tue", onboarded: 6 },
    { day: "Wed", onboarded: 5 },
    { day: "Thu", onboarded: 7 },
    { day: "Fri", onboarded: 8 },
    { day: "Sat", onboarded: 3 },
    { day: "Sun", onboarded: 2 },
  ],
};

export const engagementReport = {
  metrics: [
    { name: "Daily Active Users", value: 1240 },
    { name: "Weekly Active Users", value: 5320 },
    { name: "Avg. Session (min)", value: 5.6 },
  ],
  notifications: [
    { id: 1, title: "New review posted", time: "2h" },
    { id: 2, title: "Vendor responded to enquiry", time: "5h" },
  ],
};

