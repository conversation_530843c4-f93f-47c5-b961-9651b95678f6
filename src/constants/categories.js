// Categories and Sub-Categories sample data
export const categories = [
  {
    id: 1,
    icon: "https://akam.cdn.jdmagicbox.com/images/icons/newwap/newhotkey/restaurant-2022.svg?w=48&q=75",
    name: "Restaurants & Food",
    count: 340,
    isActive: true,
  },
  {
    id: 2,
    icon: "https://akam.cdn.jdmagicbox.com/images/icons/newwap/newhotkey/hotel-2022.svg?w=48&q=75",
    name: "Technology",
    count: 180,
    isActive: true,
  },
  {
    id: 3,
    icon: "https://akam.cdn.jdmagicbox.com/images/icons/newwap/newhotkey/dentist_2023.svg?w=48&q=75",
    name: "Healthcare",
    count: 210,
    isActive: true,
  },
  {
    id: 4,
    icon: "https://akam.cdn.jdmagicbox.com/images/icons/newwap/newhotkey/gym_2023.svg?w=48&q=75",
    name: "Services",
    count: 450,
    isActive: true,
  },
  {
    id: 5,
    icon: "https://akam.cdn.jdmagicbox.com/images/icons/newwap/newhotkey/estate-agent.svg?w=48&q=75",
    name: "Retail",
    count: 320,
    isActive: true,
  },
  {
    id: 6,
    icon: "https://akam.cdn.jdmagicbox.com/images/icons/newwap/newhotkey/hotel-2022.svg?w=48&q=75",
    name: "Construction",
    count: 150,
    isActive: false,
  },
];

export const subCategories = [
  // Restaurants & Food
  {
    id: 1,
    name: "Indian Food",
    count: 340,
    category: { id: 1, name: "Restaurants & Food" },
    icon: "https://cdn-icons-png.flaticon.com/128/3075/3075977.png",
    coverImage: "https://source.unsplash.com/800x400/?indian-food",
    isActive: true,
  },
  {
    id: 2,
    name: "Chinese Food",
    count: 180,
    category: { id: 1, name: "Restaurants & Food" },
    icon: "https://cdn-icons-png.flaticon.com/128/1046/1046784.png",
    coverImage: "https://source.unsplash.com/800x400/?chinese-food",
    isActive: true,
  },
  {
    id: 3,
    name: "Mexican Food",
    count: 210,
    category: { id: 1, name: "Restaurants & Food" },
    icon: "https://cdn-icons-png.flaticon.com/128/3075/3075973.png",
    coverImage: "https://source.unsplash.com/800x400/?mexican-food",
    isActive: true,
  },
  {
    id: 4,
    name: "Italian Food",
    count: 450,
    category: { id: 1, name: "Restaurants & Food" },
    icon: "https://cdn-icons-png.flaticon.com/128/2935/2935353.png",
    coverImage: "https://source.unsplash.com/800x400/?italian-food",
    isActive: true,
  },

  // Technology
  {
    id: 5,
    name: "Software Companies",
    count: 120,
    category: { id: 2, name: "Technology" },
    icon: "https://cdn-icons-png.flaticon.com/128/4248/4248443.png",
    coverImage: "https://source.unsplash.com/800x400/?software-company",
    isActive: true,
  },
  {
    id: 6,
    name: "IT Services",
    count: 60,
    category: { id: 2, name: "Technology" },
    icon: "https://cdn-icons-png.flaticon.com/128/3095/3095583.png",
    coverImage: "https://source.unsplash.com/800x400/?it-services",
    isActive: true,
  },
  {
    id: 7,
    name: "Tech Startups",
    count: 45,
    category: { id: 2, name: "Technology" },
    icon: "https://cdn-icons-png.flaticon.com/128/1055/1055687.png",
    coverImage: "https://source.unsplash.com/800x400/?startup",
    isActive: true,
  },

  // Healthcare
  {
    id: 8,
    name: "Dentists",
    count: 80,
    category: { id: 3, name: "Healthcare" },
    icon: "https://cdn-icons-png.flaticon.com/128/2965/2965567.png",
    coverImage: "https://source.unsplash.com/800x400/?dentist",
    isActive: true,
  },
  {
    id: 9,
    name: "General Physicians",
    count: 70,
    category: { id: 3, name: "Healthcare" },
    icon: "https://cdn-icons-png.flaticon.com/128/3771/3771576.png",
    coverImage: "https://source.unsplash.com/800x400/?doctor",
    isActive: true,
  },
  {
    id: 10,
    name: "Pathology Labs",
    count: 60,
    category: { id: 3, name: "Healthcare" },
    icon: "https://cdn-icons-png.flaticon.com/128/2785/2785819.png",
    coverImage: "https://source.unsplash.com/800x400/?lab,healthcare",
    isActive: true,
  },

  // Services
  {
    id: 11,
    name: "Home Cleaning",
    count: 160,
    category: { id: 4, name: "Services" },
    icon: "https://cdn-icons-png.flaticon.com/128/3075/3075971.png",
    coverImage: "https://source.unsplash.com/800x400/?home-cleaning",
    isActive: true,
  },
  {
    id: 12,
    name: "Pest Control",
    count: 120,
    category: { id: 4, name: "Services" },
    icon: "https://cdn-icons-png.flaticon.com/128/4202/4202845.png",
    coverImage: "https://source.unsplash.com/800x400/?pest-control",
    isActive: true,
  },
  {
    id: 13,
    name: "Plumbing Services",
    count: 90,
    category: { id: 4, name: "Services" },
    icon: "https://cdn-icons-png.flaticon.com/128/2961/2961958.png",
    coverImage: "https://source.unsplash.com/800x400/?plumber",
    isActive: true,
  },
  {
    id: 14,
    name: "Electricians",
    count: 80,
    category: { id: 4, name: "Services" },
    icon: "https://cdn-icons-png.flaticon.com/128/2961/2961948.png",
    coverImage: "https://source.unsplash.com/800x400/?electrician",
    isActive: true,
  },

  // Retail
  {
    id: 15,
    name: "Clothing Stores",
    count: 150,
    category: { id: 5, name: "Retail" },
    icon: "https://cdn-icons-png.flaticon.com/128/892/892458.png",
    coverImage: "https://source.unsplash.com/800x400/?clothing-store",
    isActive: true,
  },
  {
    id: 16,
    name: "Electronics Shops",
    count: 100,
    category: { id: 5, name: "Retail" },
    icon: "https://cdn-icons-png.flaticon.com/128/2329/2329093.png",
    coverImage: "https://source.unsplash.com/800x400/?electronics-store",
    isActive: true,
  },
  {
    id: 17,
    name: "Supermarkets",
    count: 70,
    category: { id: 5, name: "Retail" },
    icon: "https://cdn-icons-png.flaticon.com/128/3081/3081559.png",
    coverImage: "https://source.unsplash.com/800x400/?supermarket",
    isActive: true,
  },

  // Construction
  {
    id: 18,
    name: "Builders & Developers",
    count: 90,
    category: { id: 6, name: "Construction" },
    icon: "https://cdn-icons-png.flaticon.com/128/2965/2965564.png",
    coverImage: "https://source.unsplash.com/800x400/?construction-site",
    isActive: false,
  },
  {
    id: 19,
    name: "Interior Designers",
    count: 40,
    category: { id: 6, name: "Construction" },
    icon: "https://cdn-icons-png.flaticon.com/128/2312/2312011.png",
    coverImage: "https://source.unsplash.com/800x400/?interior-design",
    isActive: false,
  },
  {
    id: 20,
    name: "Architects",
    count: 20,
    category: { id: 6, name: "Construction" },
    icon: "https://cdn-icons-png.flaticon.com/128/888/888879.png",
    coverImage: "https://source.unsplash.com/800x400/?architect",
    isActive: false,
  },
];
