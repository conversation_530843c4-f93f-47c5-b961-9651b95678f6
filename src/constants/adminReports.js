// Admin reports sample data

export const userActivityReport = {
  totals: { logins: 1240, actions: 5320, failures: 24 },
  recent: [
    { id: 1, user: "<PERSON>", action: "Approved vendor", time: "2h" },
    { id: 2, user: "<PERSON>", action: "Created enquiry", time: "4h" },
    { id: 3, user: "<PERSON>", action: "Exported report", time: "1d" },
  ],
};

export const revenueReport = {
  summary: { today: 297, thisWeek: 1386, thisMonth: 4125 },
  byPlan: [
    { plan: "Starter", count: 38, revenue: 3762 },
    { plan: "Pro", count: 8, revenue: 1592 },
    { plan: "Enterprise", count: 2, revenue: 998 },
  ],
};

export const customerRegistrationsReport = {
  weekly: [
    { day: "Mon", customers: 24 },
    { day: "Tue", customers: 31 },
    { day: "Wed", customers: 18 },
    { day: "Thu", customers: 36 },
    { day: "Fri", customers: 29 },
    { day: "Sat", customers: 14 },
    { day: "Sun", customers: 12 },
  ],
  total: 164,
};

