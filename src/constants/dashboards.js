// Stats and dashboard-related sample data

/** @type {import('./sampleSchema').AdminStats} */
export const adminStats = {
  activeVendors: 1247,
  enquiriesToday: 89,
  vendorsOnboardedBySales: 34,
  pendingApprovals: 12,
};

/** @type {import('./sampleSchema').SystemStats} */
export const systemStats = {
  totalVendors: 2847,
  totalCustomers: 8934,
  totalSalesExecs: 45,
  totalRevenue: 284750,
  monthlyGrowth: { vendors: 12.5, customers: 18.2, revenue: 15.8 },
};

/** @type {import('./sampleSchema').Activity[]} */
export const recentActivities = [
  {
    id: 1,
    type: "vendor_registered",
    message: "New vendor 'TechFix Solutions' registered",
    time: "2 hours ago",
    status: "pending_approval",
  },
  {
    id: 2,
    type: "payment_received",
    message: "$99 payment received from '<PERSON>'s Kitchen'",
    time: "4 hours ago",
    status: "completed",
  },
  {
    id: 3,
    type: "admin_created",
    message: "New admin '<PERSON>' added to system",
    time: "1 day ago",
    status: "completed",
  },
];

/** @type {import('./sampleSchema').Enquiry[]} */
export const recentEnquiries = [
  {
    id: 1,
    customer: "Alice Johnson",
    vendor: "Giuseppe's Kitchen",
    message: "Looking for catering for corporate event",
    date: "2 hours ago",
    status: "new",
  },
  {
    id: 2,
    customer: "Bob Smith",
    vendor: "TechFix Solutions",
    message: "Need laptop repair services",
    date: "4 hours ago",
    status: "responded",
  },
];

